# SahAI V2 Extension Complete User Guide

## Overview
This comprehensive guide covers every aspect of the SahAI V2 Extension, from the topbar navigation to advanced features, integrations, and user interface components. The SahAI V2 Extension is a powerful AI-powered coding assistant that integrates seamlessly with VS Code and Adobe Creative Suite applications.

### Extension Architecture
The extension consists of several main components:
- **Topbar Navigation**: Main control center with 5 key elements
- **Chat Interface**: Rich messaging system with AI interaction
- **Modal System**: Unified interface for settings, history, and advanced features
- **Provider Management**: Multi-provider AI service integration
- **File & Context System**: Advanced file handling and workspace integration
- **Analytics & Monitoring**: Usage tracking and performance monitoring

## Table of Contents

### Part 1: Topbar Navigation System
- [Topbar Elements Overview](#topbar-elements-table)
- [Detailed Element Explanations](#detailed-element-explanations)
- [Modal Content Details](#modal-content-details)
- [Keyboard Shortcuts](#keyboard-shortcuts-summary)

### Part 2: Main Chat Interface
- [Chat Messages Area](#chat-messages-area)
- [Input Area](#input-area)
- [Welcome Screen](#welcome-screen)

### Part 3: Advanced Features
- [Task Management System](#task-management-system)
- [MCP Integration](#mcp-model-context-protocol-integration)
- [File and Context Management](#file-and-context-management)
- [Voice and Audio Features](#voice-and-audio-features)

### Part 4: Provider and Model Management
- [Provider Configuration](#provider-configuration-system)
- [Enhanced Proxy System](#enhanced-proxy-system)

### Part 5: User Interface Components
- [Modal System](#modal-system)
- [Theme and Styling](#theme-and-styling-system)

### Part 6: Data Management
- [Chat History System](#chat-history-system)
- [Settings and Configuration](#settings-and-configuration)

### Part 7: Analytics and Monitoring
- [Usage Analytics](#usage-analytics)
- [Health Monitoring](#health-monitoring)

### Part 8: Integration and Extensibility
- [VS Code Integration](#vs-code-integration)
- [Adobe Creative Suite Integration](#adobe-creative-suite-integration)
- [API and Extensibility](#api-and-extensibility)

---

## Part 1: Topbar Navigation System
The SahAI V2 topbar is the main navigation and control center of the extension. It contains 5 main sections with various clickable elements that open different modals and provide different functionalities.

## Topbar Elements Table

| Element | Location | Visual Appearance | Click Action | Modal/Content Opened | Keyboard Shortcut | Current Status |
|---------|----------|-------------------|--------------|---------------------|-------------------|----------------|
| **Provider Status Indicator** | Far Left | Small colored dot (Green/Red) | Opens Provider Health Modal | Shows provider connection status, health metrics, and troubleshooting info | None | ✅ Working |
| **Model Selector Button** | Center-Left | Text button showing "Provider:Model" format | Opens Model Configuration Modal | Full model selection interface with provider switching | None | ✅ Working |
| **New Chat Button** | Right Side | Plus icon (+) | Creates new chat session | No modal - direct action | Ctrl+N (Cmd+N on Mac) | ✅ Working |
| **Chat History Button** | Right Side | History/Clock icon | Opens Chat History Modal | Shows all previous chat sessions with search and management | Ctrl+H (Cmd+H on Mac) | ✅ Working |
| **Settings Button** | Far Right | Three dots (⋮) icon | Opens Settings Modal | Main settings hub with links to all sub-modals | Ctrl+M (Cmd+M on Mac) | ✅ Working |

## Detailed Element Explanations

### 1. Provider Status Indicator (Far Left)
**What it shows:**
- **Green dot**: Provider is configured, online, and ready to use
- **Red dot**: Provider is not configured, offline, or has issues

**When clicked:**
- Opens the **Provider Health Modal**
- Shows detailed connection status for the current provider
- Displays health metrics and diagnostic information
- Provides troubleshooting tips if there are issues
- Shows response times and connection quality

### 2. Model Selector Button (Center-Left)
**Display formats:**
- `"Select Provider"` - No provider selected
- `"Provider Name: Select Model"` - Provider selected but no model
- `"Provider Name: Model Name"` - Both provider and model selected
- Shows warning icon (⚠️) if provider needs configuration

**When clicked:**
- Opens the **Model Configuration Modal**
- Shows dropdown list of all available providers (OpenAI, Anthropic, OpenRouter, etc.)
- For each provider, shows:
  - Configuration status
  - API key requirement
  - Available models list
  - Model details (context length, pricing, capabilities)
- Allows switching between providers
- Allows selecting different models within a provider
- Shows API key input field for unconfigured providers

### 3. New Chat Button (Plus Icon)
**When clicked:**
- Immediately creates a new chat session
- Clears the current conversation
- Resets the chat interface
- No modal opens - direct action
- **Keyboard shortcut**: Ctrl+N (Windows/Linux) or Cmd+N (Mac)

### 4. Chat History Button (History Icon)
**When clicked:**
- Opens the **Chat History Modal**
- Shows list of all previous chat sessions
- Each session displays:
  - Session title (auto-generated from first message)
  - Date and time of last activity
  - Number of messages in the session
  - Provider used for that session
  - Preview of the conversation
- Provides search functionality to find specific conversations
- Allows sorting by: Recent, Oldest, Alphabetical
- Each session can be:
  - Clicked to load and continue that conversation
  - Deleted using the trash icon
- Shows summary: "Showing X of Y chat sessions"
- **Keyboard shortcut**: Ctrl+H (Windows/Linux) or Cmd+H (Mac)

### 5. Settings Button (Three Dots Icon)
**When clicked:**
- Opens the **Settings Modal** (main hub)
- Shows organized sections with clickable links:

#### Core Features Section:
- **📊 Analytics**: View usage statistics and performance metrics
- **⚙️ Advanced Config**: Configure advanced settings and preferences

#### Model Tools Section:
- **🔄 Model Comparison**: Compare different AI models side by side
- **🤖 Multi-Model Chat**: Chat with multiple models simultaneously

#### Help & Support Section:
- **❓ Help & Support**: Get help and find answers to common questions
- **ℹ️ About**: Learn more about SahAI Extension

**Keyboard shortcut**: Ctrl+M (Windows/Linux) or Cmd+M (Mac)

## Modal Content Details

### Provider Health Modal
**Contains:**
- Current provider connection status
- Response time metrics
- Error logs and diagnostics
- Troubleshooting suggestions
- Provider-specific health information

### Model Configuration Modal
**Contains:**
- Provider dropdown with all available options:
  - OpenAI (GPT-4, GPT-3.5, etc.)
  - Anthropic (Claude models)
  - OpenRouter (Multiple providers)
  - Google (Gemini models)
  - And more...
- For each provider:
  - API key configuration field
  - Model selection dropdown
  - Model information (context length, pricing)
  - Configuration status indicators
- Search functionality for models
- Real-time model loading with enhanced proxy support

### Chat History Modal
**Contains:**
- Search bar for finding specific conversations
- Sort dropdown (Recent, Oldest, Alphabetical)
- List of chat sessions showing:
  - Session title
  - Last activity timestamp
  - Message count
  - Provider used
  - Conversation preview
- Delete button for each session
- Session summary statistics

### Settings Modal (Main Hub)
**Contains:**
- Organized sections with clickable links to sub-modals
- Each link shows icon, title, and description
- Navigation to specialized configuration areas

### Analytics Modal
**Contains:**
- Usage statistics dashboard
- Total messages and sessions count
- Average response times
- Most used provider and model
- Token usage estimates
- Cost estimates
- Time range selector (7d, 30d, 90d, All time)
- Performance metrics and trends

### Advanced Config Modal
**Contains:**
- Performance settings (max tokens, temperature, etc.)
- Behavior settings (streaming, auto-save, confirmations)
- Debug settings (debug mode, API logging)
- UI settings (compact mode, animations, font size)
- Privacy settings (data storage, analytics sharing)

### Model Comparison Modal
**Contains:**
- Side-by-side model comparison interface
- Performance metrics comparison
- Response quality analysis
- Cost comparison
- Speed benchmarks

### Multi-Model Chat Modal
**Contains:**
- Interface for chatting with multiple models simultaneously
- Model selection for parallel conversations
- Response comparison view
- Unified input for all selected models

### Help Modal
**Contains:**
- FAQ section with common questions and answers
- Topics covered:
  - API key setup
  - Adobe application support
  - Model comparison usage
  - Multi-model chat
  - Troubleshooting
  - Data security
- Pro tips section with keyboard shortcuts and usage tips
- Getting started guide

### About Modal
**Contains:**
- Application information (SahAI Extension v2.0.0)
- Feature highlights with icons and descriptions
- Version information
- Links to documentation, GitHub, privacy policy, terms of service
- Credits and acknowledgments

## Keyboard Shortcuts Summary

| Shortcut | Action | Description |
|----------|--------|-------------|
| **Ctrl+N** (Cmd+N) | New Chat | Creates a new chat session |
| **Ctrl+H** (Cmd+H) | Chat History | Opens chat history modal |
| **Ctrl+M** (Cmd+M) | Settings | Opens main settings modal |
| **Ctrl+,** (Cmd+,) | Settings | Alternative settings shortcut |
| **Enter** | Send Message | Sends the current message in input area |
| **Shift+Enter** | New Line | Adds new line in message input |

## Current Implementation Status

✅ **Fully Working:**
- All topbar elements are clickable and functional
- All modals open and display content correctly
- Keyboard shortcuts work as expected
- Provider status indicator shows real status
- Model configuration with enhanced proxy support (319+ models from OpenRouter without API key)
- Chat history with full session management
- Settings hub with all sub-modal navigation

✅ **Enhanced Features:**
- Smart proxy server for model loading without API keys
- Real-time provider health monitoring
- Advanced caching for improved performance
- Background model refresh every 5 minutes
- Comprehensive error handling and fallbacks

---

## Part 2: Main Chat Interface Components

### Chat Messages Area
**Location**: Center of the interface, below the topbar
**Purpose**: Displays the conversation between user and AI

**Features:**
- **Message Rendering**: Rich text display with markdown support
- **Code Block Highlighting**: Syntax highlighting for code snippets
- **Code Execution**: Interactive code blocks with run/copy/edit actions
- **Message Actions**: Each message has hover actions:
  - Copy message content
  - Edit message (for user messages)
  - Delete message
  - Quote message for reference
  - Expand/collapse long messages
- **Auto-scroll**: Automatically scrolls to new messages
- **Message Interactions**: Text selection with context menu for copy/quote
- **Empty State**: Shows welcome message when no conversation exists

**Message Types:**
- **User Messages**: Blue bubble on the right side
- **Assistant Messages**: Gray bubble on the left side with AI avatar
- **System Messages**: Centered messages for system notifications
- **Error Messages**: Red-tinted messages for error states

### Input Area
**Location**: Bottom of the interface
**Purpose**: Text input and message composition

**Components:**
1. **Text Area**:
   - Auto-resizing textarea (max 200px height)
   - Character counter (max 4000 characters)
   - Placeholder text: "Type a message..."
   - Supports multi-line input with Shift+Enter
   - Composition support for international keyboards

2. **Left Action Buttons**:
   - **Attach File Button** (📎): Upload files and images
     - Supports: Images (PNG, JPG, JPEG, WEBP)
     - Documents: XML, JSON, TXT, LOG, MD, DOCX, PDF, XLSX, CSV
     - Multiple file selection
     - Drag & drop support
   - **Context Reference Button** (🔗): Reference code context
     - Links to current file/selection
     - Workspace context integration

3. **Right Action Buttons**:
   - **Voice Input Button** (🎤): Start voice recording
     - Speech-to-text conversion
     - Real-time transcription
     - Voice command recognition
   - **Send Button** (➤): Send message
     - Disabled when input is empty
     - Shows loading spinner during processing
     - Keyboard shortcut: Enter (Shift+Enter for new line)

**Keyboard Shortcuts:**
- **Enter**: Send message
- **Shift+Enter**: New line in input
- **Ctrl+A**: Select all text in input
- **Escape**: Clear input (when focused)

### Welcome Screen
**Location**: Replaces chat area when no conversation exists
**Purpose**: Onboarding and quick start

**Components:**
1. **Welcome Header**:
   - SahAI logo and branding
   - Version information
   - Welcome message

2. **Quick Start Options**:
   - **Get Started for Free**: Account creation
   - **Use API Key**: Direct API configuration
   - **Featured Models**: Highlighted model recommendations

3. **Suggested Tasks**:
   - Pre-written prompts for common use cases
   - Code analysis tasks
   - Creative writing prompts
   - Technical assistance examples

4. **Recent History Preview**:
   - Shows last 3 chat sessions
   - Quick access to continue conversations
   - "View All History" link to full history modal

---

## Part 3: Advanced Features

### Task Management System
**Purpose**: Organize and track complex multi-step tasks

**Components:**
1. **Task Header**:
   - Task title and description
   - Progress indicator
   - Time tracking
   - Status badges (In Progress, Completed, Failed)

2. **Task Actions**:
   - Pause/Resume task execution
   - Cancel current task
   - View task details
   - Export task results

3. **Subtask Breakdown**:
   - Hierarchical task structure
   - Individual subtask status
   - Dependencies visualization
   - Progress tracking per subtask

### MCP (Model Context Protocol) Integration
**Purpose**: Extend AI capabilities with external tools and services

**Features:**
1. **MCP Server Management**:
   - Add/remove MCP servers
   - Server configuration and authentication
   - Health monitoring and status
   - Capability discovery

2. **Tool Integration**:
   - Dynamic tool loading from MCP servers
   - Tool parameter configuration
   - Execution monitoring
   - Result processing and display

3. **Context Enhancement**:
   - External data source integration
   - Real-time information access
   - Custom workflow automation
   - API endpoint connections

### File and Context Management
**Purpose**: Handle file uploads, context references, and workspace integration

**File Upload System:**
- **Supported Formats**: Images, documents, code files, data files
- **Processing**: Automatic content extraction and analysis
- **Preview**: Thumbnail generation for images
- **Management**: File list with remove/replace options
- **Size Limits**: Configurable per file type
- **Security**: File type validation and sanitization

**Context Reference System:**
- **Workspace Integration**: Access to current VS Code workspace
- **File Context**: Reference specific files or code sections
- **Selection Context**: Use current editor selection
- **Project Context**: Understand project structure and dependencies
- **Git Integration**: Access to version control information

### Voice and Audio Features
**Purpose**: Voice input and audio processing capabilities

**Voice Input:**
- **Speech Recognition**: Real-time speech-to-text
- **Language Support**: Multiple language recognition
- **Noise Cancellation**: Background noise filtering
- **Voice Commands**: Predefined command recognition
- **Continuous Listening**: Hands-free operation mode

**Audio Processing:**
- **Audio File Upload**: Support for common audio formats
- **Transcription**: Convert audio files to text
- **Audio Analysis**: Content analysis and summarization
- **Voice Synthesis**: Text-to-speech for responses (planned)

---

## Part 4: Provider and Model Management

### Provider Configuration System
**Purpose**: Manage multiple AI service providers and their configurations

**Supported Providers:**
1. **OpenAI**: GPT-4, GPT-3.5, DALL-E models
2. **Anthropic**: Claude 3.5 Sonnet, Claude 3 Haiku, Claude 3 Opus
3. **OpenRouter**: 319+ models from various providers
4. **Google**: Gemini Pro, Gemini Flash models
5. **Mistral**: Mistral Large, Mistral Medium models
6. **Groq**: Fast inference models
7. **xAI**: Grok models with real-time information
8. **Local Models**: Ollama integration for local inference
9. **Custom APIs**: OpenAI-compatible API endpoints

**Configuration Features:**
- **API Key Management**: Secure storage and validation
- **Base URL Configuration**: Custom endpoint support
- **Model Selection**: Provider-specific model lists
- **Parameter Tuning**: Temperature, max tokens, top-p settings
- **Cost Tracking**: Usage monitoring and cost estimation
- **Health Monitoring**: Real-time provider status checking

### Enhanced Proxy System
**Purpose**: Access models without API keys through intelligent proxy

**Features:**
- **OpenRouter Integration**: 319+ models available without API key
- **Smart Routing**: Automatic model selection based on availability
- **Load Balancing**: Distribute requests across multiple endpoints
- **Fallback System**: Automatic failover to alternative providers
- **Caching**: Response caching for improved performance
- **Rate Limiting**: Intelligent request throttling

---

## Part 5: User Interface Components

### Modal System
**Purpose**: Unified modal interface for all extension features

**Modal Types:**
1. **Slide-in Modals**: Main interface modals (Settings, History, etc.)
2. **Popup Modals**: Quick actions and confirmations
3. **Full-screen Modals**: Complex interfaces (Model Comparison, Multi-Model Chat)
4. **Overlay Modals**: Context-sensitive help and information

**Modal Features:**
- **Consistent Design**: Unified styling across all modals
- **Keyboard Navigation**: Full keyboard accessibility
- **Responsive Layout**: Adapts to different screen sizes
- **State Management**: Preserves modal state during navigation
- **Animation System**: Smooth transitions and micro-interactions

### Theme and Styling System
**Purpose**: Consistent visual design and theme support

**Theme Features:**
- **VS Code Integration**: Matches VS Code theme automatically
- **Dark/Light Mode**: Automatic theme switching
- **Custom Themes**: User-defined color schemes
- **Accessibility**: High contrast and screen reader support
- **Responsive Design**: Mobile and tablet compatibility

**Visual Components:**
- **Icons**: Consistent icon library with multiple sizes
- **Typography**: Hierarchical text styling system
- **Colors**: Semantic color system with theme variants
- **Spacing**: Consistent spacing and layout grid
- **Animations**: Subtle micro-interactions and transitions

---

## Part 6: Data Management and Storage

### Chat History System
**Purpose**: Persistent storage and management of conversation history

**Features:**
- **Session Management**: Automatic session creation and tracking
- **Search Functionality**: Full-text search across all conversations
- **Filtering**: Filter by date, provider, model, or content type
- **Export Options**: Export conversations in multiple formats
- **Import System**: Import conversations from other tools
- **Backup/Restore**: Cloud backup and local backup options

**Storage Details:**
- **Local Storage**: Browser localStorage for quick access
- **Persistent Storage**: File-based storage for long-term retention
- **Encryption**: Optional encryption for sensitive conversations
- **Compression**: Automatic compression for large conversations
- **Cleanup**: Automatic cleanup of old conversations

### Settings and Configuration
**Purpose**: Comprehensive settings management system

**Settings Categories:**
1. **General Settings**:
   - Default provider and model
   - Language preferences
   - Notification settings
   - Auto-save configuration

2. **Performance Settings**:
   - Response streaming
   - Caching preferences
   - Background refresh intervals
   - Memory usage limits

3. **Privacy Settings**:
   - Data retention policies
   - Analytics sharing preferences
   - Local vs cloud storage
   - Conversation encryption

4. **Advanced Settings**:
   - Debug mode
   - API logging
   - Custom endpoints
   - Experimental features

---

## Part 7: Analytics and Monitoring

### Usage Analytics
**Purpose**: Track usage patterns and performance metrics

**Metrics Tracked:**
- **Message Statistics**: Total messages, sessions, response times
- **Provider Usage**: Most used providers and models
- **Cost Analysis**: Token usage and cost estimates
- **Performance Metrics**: Response times, error rates, success rates
- **Feature Usage**: Most used features and modals
- **Time Analysis**: Usage patterns by time of day/week

**Analytics Dashboard:**
- **Overview Cards**: Key metrics at a glance
- **Time Series Charts**: Usage trends over time
- **Provider Comparison**: Performance comparison across providers
- **Cost Breakdown**: Detailed cost analysis by provider/model
- **Export Options**: Export analytics data for external analysis

### Health Monitoring
**Purpose**: Monitor system health and provider status

**Monitoring Features:**
- **Provider Health**: Real-time status of all configured providers
- **Response Time Tracking**: Monitor API response times
- **Error Rate Monitoring**: Track and analyze error patterns
- **Uptime Tracking**: Provider availability monitoring
- **Alert System**: Notifications for service issues
- **Diagnostic Tools**: Built-in troubleshooting utilities

---

## Part 8: Integration and Extensibility

### VS Code Integration
**Purpose**: Deep integration with VS Code editor and workspace

**Integration Features:**
- **Workspace Context**: Access to current workspace files and structure
- **Editor Integration**: Reference current file, selection, or cursor position
- **Command Palette**: VS Code command integration
- **Status Bar**: Extension status and quick actions
- **Sidebar Panel**: Dedicated extension panel
- **Settings Sync**: Sync with VS Code settings

### Adobe Creative Suite Integration
**Purpose**: Native integration with Adobe applications

**CEP (Common Extensibility Platform) Features:**
- **Panel Integration**: Native panels in Adobe applications
- **Theme Matching**: Automatic theme matching with Adobe UI
- **Document Context**: Access to current document and selection
- **Asset Integration**: Work with Adobe assets and libraries
- **Workflow Automation**: Automate common creative tasks
- **Script Generation**: Generate ExtendScript for Adobe applications

### API and Extensibility
**Purpose**: Allow third-party extensions and integrations

**Extension Points:**
- **Custom Providers**: Add new AI service providers
- **Message Renderers**: Custom message display components
- **Tool Integration**: Add new tools and capabilities
- **Theme Extensions**: Custom themes and styling
- **Workflow Plugins**: Custom automation workflows

---

## Complete Implementation Status

### ✅ Fully Implemented and Working:
- **Topbar Navigation**: All 5 elements functional with proper modal integration
- **Chat Interface**: Rich messaging with markdown, code highlighting, and interactions
- **Input System**: Multi-line input with file attachments and voice input support
- **Modal System**: Unified slide-in modals with consistent navigation
- **Provider Management**: 9+ AI providers with 319+ models via OpenRouter proxy
- **Settings System**: Comprehensive configuration with real-time updates
- **Chat History**: Full session management with search and export
- **Theme Integration**: VS Code theme matching and custom styling
- **Keyboard Shortcuts**: Complete keyboard navigation support
- **File Handling**: Multi-format file upload and processing
- **Context Integration**: VS Code workspace and selection context
- **Health Monitoring**: Real-time provider status and diagnostics
- **Analytics Dashboard**: Usage tracking and performance metrics
- **Error Handling**: Comprehensive error recovery and user feedback

### ✅ Enhanced Features:
- **Smart Proxy System**: Access 319+ models without API keys
- **Real-time Health Monitoring**: Provider status with automatic failover
- **Advanced Caching**: Response caching for improved performance
- **Background Refresh**: Automatic model list updates every 5 minutes
- **Intelligent Routing**: Load balancing across multiple endpoints
- **Cost Optimization**: Token usage tracking and cost estimation
- **Security**: Encrypted storage for sensitive configuration data
- **Accessibility**: Full keyboard navigation and screen reader support
- **Performance**: Optimized rendering and memory management
- **Extensibility**: Plugin architecture for custom providers and tools

### 🚀 Advanced Integrations:
- **VS Code Deep Integration**: Workspace context, editor integration, command palette
- **Adobe CEP Integration**: Native panels in Creative Suite applications
- **MCP Protocol Support**: External tool integration and capability extension
- **Git Integration**: Version control context and history access
- **Task Management**: Complex multi-step task organization and tracking
- **Voice Processing**: Speech-to-text and voice command recognition
- **Multi-Model Chat**: Parallel conversations with multiple AI models
- **Model Comparison**: Side-by-side performance and quality analysis

### 📊 Comprehensive Analytics:
- **Usage Metrics**: Message counts, session tracking, response times
- **Cost Analysis**: Token usage and cost breakdown by provider/model
- **Performance Monitoring**: Response times, error rates, success metrics
- **Provider Comparison**: Detailed performance analysis across providers
- **Time-based Analytics**: Usage patterns and trend analysis
- **Export Capabilities**: Data export for external analysis tools

### 🔧 Developer Features:
- **Debug Mode**: Detailed logging and diagnostic information
- **API Testing**: Built-in tools for testing provider configurations
- **Custom Endpoints**: Support for self-hosted and custom AI services
- **Plugin Development**: SDK for creating custom extensions
- **Theme Development**: Tools for creating custom themes and styling
- **Workflow Automation**: Scriptable automation for common tasks

---

## Summary

The SahAI V2 Extension is a comprehensive AI-powered coding assistant that provides:

1. **Universal AI Access**: Support for 9+ major AI providers with 319+ models
2. **Rich User Interface**: Modern, accessible interface with VS Code integration
3. **Advanced Features**: Task management, multi-model chat, voice input, file processing
4. **Deep Integration**: Native VS Code and Adobe Creative Suite integration
5. **Extensibility**: Plugin architecture for custom tools and providers
6. **Analytics**: Comprehensive usage tracking and performance monitoring
7. **Security**: Encrypted storage and secure API key management
8. **Performance**: Optimized for speed with intelligent caching and routing

This guide covers every clickable element, feature, and capability of the SahAI V2 Extension, providing users with complete understanding of all available functionality and how to effectively use the extension for their AI-powered development and creative workflows.
